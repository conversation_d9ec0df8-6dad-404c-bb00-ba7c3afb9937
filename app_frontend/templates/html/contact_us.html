{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - HiSage Health</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
/* Contact Us Styles - Home Page Style */
:root {
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --secondary-blue: #3b82f6;
    --accent-teal: #0d9488;
    --accent-green: #059669;
    --text-dark: #1f2937;
    --text-gray: #6b7280;
    --text-light: #9ca3af;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --border-light: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.section-subtitle {
    font-size: 1.25rem;
    text-align: center;
    color: var(--text-gray);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}



/* Card Styles */
.card {
    background: var(--bg-white);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: 2rem;
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: 2rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--bg-white);
    color: var(--text-dark);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--bg-white);
    color: var(--text-dark);
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #f0fdf4;
    color: var(--accent-green);
    border-color: #bbf7d0;
}

.alert-danger {
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

.alert-info {
    background-color: #eff6ff;
    color: var(--primary-blue);
    border-color: #dbeafe;
}



/* Responsive Design */
@media (max-width: 768px) {
    .section {
        padding: 60px 0;
    }
    
    .card-header,
    .card-body {
        padding: 1.5rem;
    }
}

/* Auto-filled field styles */
.readonly-field {
    background-color: var(--bg-light) !important;
    border-color: var(--border-light) !important;
    color: var(--text-gray) !important;
}

.auto-filled-badge {
    font-size: 0.75rem;
    margin-left: 0.5rem;
    color: var(--accent-green) !important;
    font-weight: 500;
}
    </style>
</head>
<body>
<!-- Contact Form Section -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Alert Messages -->
                <div id="message-container"></div>

                <!-- Contact Form Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="section-title mb-0">Send us a Message</h2>
                        <p class="section-subtitle mb-0">Fill out the form below and we'll get back to you within 48 hours.</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Login Status -->
                        <div id="login-prompt" class="alert alert-info" style="display: none;">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> You can contact us without logging in, but if you have an account,
                            <a href="#" class="alert-link" data-login-link="true">please log in</a> for a better experience.
                        </div>

                        <form id="contact-form">
                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Full Name *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" placeholder="Enter your full name..." required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email Address *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email address..." required>
                            </div>
                            
                            <div class="form-group">
                                <label for="inquiry_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Inquiry Type
                                </label>
                                <select class="form-select" id="inquiry_type" name="inquiry_type">
                                    <option value="0">General Inquiry</option>
                                    <option value="1">Technical Support</option>
                                    <option value="2">Business Partnership</option>
                                    <option value="3">Media & Press</option>
                                    <option value="4">Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Message *
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="6" 
                                        placeholder="Please describe your inquiry in detail..." required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<script>
    // Configuration
    const API_BASE_URL = '{{ API_BASE_URL }}';
    const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';

    // Authentication functions
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token || token === 'null' || token === '') {
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user_info');
                return false;
            }

            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            return false;
        }
    }

    function getCurrentUser() {
        const userInfo = localStorage.getItem('user_info');
        return userInfo ? JSON.parse(userInfo) : null;
    }

    // Update user status display
    function updateUserStatus() {
        const loginPrompt = document.getElementById('login-prompt');
        const loginLink = document.getElementById('login-link');

        if (isUserAuthenticated()) {
            const user = getCurrentUser();
            if (user && user.email) {
                loginPrompt.style.display = 'none';

                // Auto-fill form if user is logged in
                document.getElementById('name').value = user.first_name && user.last_name ?
                    `${user.first_name} ${user.last_name}` : user.email.split('@')[0];
                document.getElementById('email').value = user.email;

                // Make fields readonly and add visual indication
                document.getElementById('name').classList.add('readonly-field');
                document.getElementById('email').classList.add('readonly-field');
                document.getElementById('name').readOnly = true;
                document.getElementById('email').readOnly = true;

                // Add badges to show auto-filled
                if (!document.querySelector('.auto-filled-badge')) {
                    const nameBadge = document.createElement('span');
                    nameBadge.className = 'auto-filled-badge';
                    nameBadge.innerHTML = '<i class="fas fa-check-circle"></i> Auto-filled';
                    document.querySelector('label[for="name"]').appendChild(nameBadge);

                    const emailBadge = document.createElement('span');
                    emailBadge.className = 'auto-filled-badge';
                    emailBadge.innerHTML = '<i class="fas fa-check-circle"></i> Auto-filled';
                    document.querySelector('label[for="email"]').appendChild(emailBadge);
                }
            }
        } else {
            loginPrompt.style.display = 'block';
            loginLink.href = '/login/';
        }
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Contact Us page initialized');
        console.log('🔐 User authenticated:', isUserAuthenticated());
        
        updateUserStatus();
        setupContactForm();
    });

    // Setup contact form
    function setupContactForm() {
        const form = document.getElementById('contact-form');
        form.addEventListener('submit', handleContactSubmit);
    }

    // Handle contact form submission
    async function handleContactSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);

        // Show loading state
        const submitButton = e.target.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
        submitButton.disabled = true;

        try {
            const response = await fetch(`${LOCAL_BASE_URL}/contact-us/api/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();

            if (data.success) {
                showAlert('Message sent successfully! We will respond within 48 hours.', 'success');
                e.target.reset();
                updateUserStatus(); // Re-fill form if user is logged in
            } else {
                showAlert('Failed to send message: ' + (data.error || 'Unknown error'), 'danger');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            showAlert('Failed to send message. Please try again.', 'danger');
        } finally {
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        }
    }

    // Show alert message
    function showAlert(message, type) {
        const container = document.getElementById('message-container');
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
        `;
        
        container.innerHTML = '';
        container.appendChild(alertDiv);
        
        // Scroll to message
        container.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // CSRF Token function
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
</body>
</html>
{% endblock %}
